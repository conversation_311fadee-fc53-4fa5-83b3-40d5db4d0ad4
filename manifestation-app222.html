<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 - 科技感UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: auto;
            padding: 20px;
        }

        .container {
            display: flex;
            gap: 20px;
            min-width: 1540px;
        }

        .phone {
            width: 375px;
            height: 812px;
            border: 1px solid #333;
            border-radius: 40px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 0 50px rgba(0, 255, 255, 0.1),
                inset 0 0 50px rgba(255, 255, 255, 0.05);
        }

        /* SVG图标系统 */
        .icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        /* 玻璃拟态效果 */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }

        /* 悬浮卡片效果 */
        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s ease;
            filter: drop-shadow(0 8px 32px rgba(0, 255, 255, 0.1));
        }

        .card:hover {
            transform: translateY(-5px);
            filter: drop-shadow(0 15px 40px rgba(0, 255, 255, 0.2));
            border-color: rgba(0, 255, 255, 0.3);
        }

        /* 启动页样式 */
        .splash-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: radial-gradient(circle at center, #1a1a2e 0%, #0f0f23 100%);
            position: relative;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.5)); }
            to { filter: drop-shadow(0 0 30px rgba(255, 0, 255, 0.8)); }
        }

        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ffff;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }

        /* 主控制台样式 */
        .dashboard {
            padding: 60px 20px 20px;
            height: 100%;
            overflow-y: auto;
        }

        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #00ffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .header p {
            color: #888;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(0, 255, 255, 0.1);
            border-color: rgba(0, 255, 255, 0.3);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #00ffff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 动态折线图 */
        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #00ffff;
        }

        .chart-line {
            stroke: #00ffff;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 3s ease-in-out forwards;
        }

        @keyframes drawLine {
            to { stroke-dashoffset: 0; }
        }

        /* 额外动画效果 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes progressGlow {
            from { box-shadow: 0 0 5px rgba(0, 255, 255, 0.5); }
            to { box-shadow: 0 0 20px rgba(255, 0, 255, 0.8); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 按钮悬浮效果 */
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        }

        /* 响应式调整 */
        @media (max-width: 1600px) {
            .container {
                min-width: auto;
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="home" viewBox="0 0 24 24">
                <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/>
            </symbol>
            <symbol id="chart" viewBox="0 0 24 24">
                <path d="M3 3v18h18M7 12l4-4 4 4 4-4"/>
            </symbol>
            <symbol id="user" viewBox="0 0 24 24">
                <circle cx="12" cy="8" r="4"/>
                <path d="M6 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2"/>
            </symbol>
            <symbol id="settings" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3"/>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <!-- 启动页 -->
        <div class="phone">
            <div class="splash-screen">
                <div class="particles">
                    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                    <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
                    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
                    <div class="particle" style="left: 40%; animation-delay: 0.5s;"></div>
                    <div class="particle" style="left: 50%; animation-delay: 1.5s;"></div>
                    <div class="particle" style="left: 60%; animation-delay: 2.5s;"></div>
                    <div class="particle" style="left: 70%; animation-delay: 0.8s;"></div>
                    <div class="particle" style="left: 80%; animation-delay: 1.8s;"></div>
                    <div class="particle" style="left: 90%; animation-delay: 2.8s;"></div>
                </div>
                <div class="logo">显化应用</div>
                <p style="margin-top: 20px; color: #888; font-size: 16px;">科技赋能 · 梦想成真</p>
            </div>
        </div>

        <!-- 主控制台 -->
        <div class="phone">
            <div class="status-bar">
                <span>9:41</span>
                <span>显化控制台</span>
                <span>100%</span>
            </div>
            <div class="dashboard">
                <div class="header">
                    <h1>能量监控</h1>
                    <p>实时显化数据分析</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">87%</div>
                        <div class="stat-label">显化成功率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">142</div>
                        <div class="stat-label">活跃目标</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">9.2k</div>
                        <div class="stat-label">能量值</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">28天</div>
                        <div class="stat-label">连续练习</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">能量波动趋势</div>
                    <svg width="100%" height="120" viewBox="0 0 300 120">
                        <defs>
                            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.3"/>
                                <stop offset="100%" style="stop-color:#00ffff;stop-opacity:0"/>
                            </linearGradient>
                        </defs>
                        <path class="chart-line" d="M10,80 Q50,20 100,40 T200,30 T290,50" />
                        <path d="M10,80 Q50,20 100,40 T200,30 T290,50 L290,110 L10,110 Z" fill="url(#chartGradient)"/>
                    </svg>
                </div>

                <div class="card" style="margin-bottom: 20px;">
                    <h3 style="color: #00ffff; margin-bottom: 15px; font-size: 18px;">今日显化目标</h3>
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                        <span>财富丰盛</span>
                        <span style="color: #00ffff;">75%</span>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); height: 6px; border-radius: 3px; overflow: hidden;">
                        <div style="background: linear-gradient(90deg, #00ffff, #ff00ff); height: 100%; width: 75%; border-radius: 3px; animation: progressGlow 2s ease-in-out infinite alternate;"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="color: #00ffff; margin-bottom: 15px; font-size: 18px;">能量提升建议</h3>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <div style="width: 8px; height: 8px; background: #00ffff; border-radius: 50%; margin-right: 12px;"></div>
                        <span style="font-size: 14px;">冥想练习 15分钟</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <div style="width: 8px; height: 8px; background: #ff00ff; border-radius: 50%; margin-right: 12px;"></div>
                        <span style="font-size: 14px;">感恩日记记录</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 8px; height: 8px; background: #ffff00; border-radius: 50%; margin-right: 12px;"></div>
                        <span style="font-size: 14px;">正向肯定语句</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 显化工具页 -->
        <div class="phone">
            <div class="status-bar">
                <span>9:41</span>
                <span>显化工具</span>
                <span>100%</span>
            </div>
            <div class="dashboard">
                <div class="header">
                    <h1>显化工具箱</h1>
                    <p>选择您的显化方式</p>
                </div>

                <div class="card" style="margin-bottom: 20px; text-align: center;">
                    <div style="width: 80px; height: 80px; margin: 0 auto 15px; background: linear-gradient(45deg, #00ffff, #ff00ff); border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: pulse 2s ease-in-out infinite;">
                        <svg class="icon" style="width: 40px; height: 40px; color: white;">
                            <use href="#chart"></use>
                        </svg>
                    </div>
                    <h3 style="color: #00ffff; margin-bottom: 10px;">视觉化显化</h3>
                    <p style="color: #888; font-size: 14px; line-height: 1.5;">通过强大的视觉化技术，将您的目标转化为现实</p>
                    <button style="margin-top: 15px; background: linear-gradient(45deg, #00ffff, #ff00ff); border: none; color: white; padding: 12px 24px; border-radius: 25px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">开始显化</button>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div class="card" style="text-align: center; padding: 15px;">
                        <div style="width: 50px; height: 50px; margin: 0 auto 10px; background: rgba(0,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <span style="font-size: 24px;">🧘</span>
                        </div>
                        <h4 style="color: #00ffff; font-size: 14px; margin-bottom: 5px;">冥想引导</h4>
                        <p style="color: #888; font-size: 12px;">深度冥想体验</p>
                    </div>
                    <div class="card" style="text-align: center; padding: 15px;">
                        <div style="width: 50px; height: 50px; margin: 0 auto 10px; background: rgba(255,0,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <span style="font-size: 24px;">📝</span>
                        </div>
                        <h4 style="color: #ff00ff; font-size: 14px; margin-bottom: 5px;">愿望清单</h4>
                        <p style="color: #888; font-size: 12px;">记录您的目标</p>
                    </div>
                </div>

                <div class="card">
                    <h3 style="color: #00ffff; margin-bottom: 15px; font-size: 18px;">能量调频器</h3>
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span style="font-size: 14px;">爱与感恩</span>
                            <span style="color: #00ffff; font-size: 14px;">528Hz</span>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); height: 8px; border-radius: 4px; position: relative;">
                            <div style="background: linear-gradient(90deg, #00ffff, #ffffff); height: 100%; width: 70%; border-radius: 4px;"></div>
                            <div style="position: absolute; right: 30%; top: -2px; width: 12px; height: 12px; background: #00ffff; border-radius: 50%; box-shadow: 0 0 10px rgba(0,255,255,0.5);"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span style="font-size: 14px;">丰盛富足</span>
                            <span style="color: #ff00ff; font-size: 14px;">888Hz</span>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); height: 8px; border-radius: 4px; position: relative;">
                            <div style="background: linear-gradient(90deg, #ff00ff, #ffffff); height: 100%; width: 85%; border-radius: 4px;"></div>
                            <div style="position: absolute; right: 15%; top: -2px; width: 12px; height: 12px; background: #ff00ff; border-radius: 50%; box-shadow: 0 0 10px rgba(255,0,255,0.5);"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人中心页 -->
        <div class="phone">
            <div class="status-bar">
                <span>9:41</span>
                <span>个人中心</span>
                <span>100%</span>
            </div>
            <div class="dashboard">
                <div class="card" style="margin-bottom: 30px; text-align: center;">
                    <div style="width: 100px; height: 100px; margin: 0 auto 15px; background: linear-gradient(45deg, #00ffff, #ff00ff); border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative;">
                        <image href="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" width="90" height="90" style="border-radius: 50%;"/>
                        <div style="position: absolute; bottom: 5px; right: 5px; width: 20px; height: 20px; background: #00ff00; border-radius: 50%; border: 2px solid #1a1a2e;"></div>
                    </div>
                    <h2 style="color: #ffffff; margin-bottom: 5px;">显化大师</h2>
                    <p style="color: #888; font-size: 14px;">Lv.8 · 连续显化28天</p>
                    <div style="margin-top: 15px; display: flex; justify-content: center; gap: 20px;">
                        <div style="text-align: center;">
                            <div style="color: #00ffff; font-size: 20px; font-weight: 700;">156</div>
                            <div style="color: #888; font-size: 12px;">已实现</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #ff00ff; font-size: 20px; font-weight: 700;">89%</div>
                            <div style="color: #888; font-size: 12px;">成功率</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="color: #ffff00; font-size: 20px; font-weight: 700;">2.1k</div>
                            <div style="color: #888; font-size: 12px;">能量点</div>
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div class="card" style="text-align: center; padding: 15px;">
                        <svg class="icon" style="width: 30px; height: 30px; color: #00ffff; margin-bottom: 10px;">
                            <use href="#chart"></use>
                        </svg>
                        <h4 style="color: #00ffff; font-size: 14px; margin-bottom: 5px;">数据统计</h4>
                        <p style="color: #888; font-size: 12px;">查看详细报告</p>
                    </div>
                    <div class="card" style="text-align: center; padding: 15px;">
                        <svg class="icon" style="width: 30px; height: 30px; color: #ff00ff; margin-bottom: 10px;">
                            <use href="#settings"></use>
                        </svg>
                        <h4 style="color: #ff00ff; font-size: 14px; margin-bottom: 5px;">设置中心</h4>
                        <p style="color: #888; font-size: 12px;">个性化配置</p>
                    </div>
                </div>

                <div class="card" style="margin-bottom: 20px;">
                    <h3 style="color: #00ffff; margin-bottom: 15px; font-size: 18px;">成就徽章</h3>
                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #ffd700, #ffed4e); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; animation: rotate 3s linear infinite;">🏆</div>
                        <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #c0c0c0, #e8e8e8); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px;">⭐</div>
                        <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #cd7f32, #daa520); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px;">💎</div>
                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.1); border: 2px dashed rgba(255,255,255,0.3); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; opacity: 0.5;">🔒</div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="color: #00ffff; margin-bottom: 15px; font-size: 18px;">快捷操作</h3>
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 18px;">🔔</span>
                                <span>通知设置</span>
                            </div>
                            <span style="color: #888;">></span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 18px;">📊</span>
                                <span>数据导出</span>
                            </div>
                            <span style="color: #888;">></span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 18px;">❓</span>
                                <span>帮助中心</span>
                            </div>
                            <span style="color: #888;">></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 粒子动画随机位置
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 3 + 's';
            });

            // 卡片点击效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 按钮点击效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = e.offsetX - 10 + 'px';
                    ripple.style.top = e.offsetY - 10 + 'px';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';

                    this.style.position = 'relative';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // 添加涟漪动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>

</body>
</html>
