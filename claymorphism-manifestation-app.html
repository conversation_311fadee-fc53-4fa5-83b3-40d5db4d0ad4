<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 - 大地黏土风UI原型</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: #E6D5C7;
            padding: 20px;
            min-height: 100vh;
            color: #5D4037;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #ccc;
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            background: #E6D5C7;
            box-shadow: 
                0 20px 40px rgba(93, 64, 55, 0.15),
                inset 0 1px 0 rgba(250, 243, 233, 0.5);
        }

        /* 3D黏土风格基础样式 */
        .clay-card {
            background: #E6D5C7;
            border-radius: 20px;
            padding: 20px;
            margin: 10px;
            box-shadow: 
                8px 8px 16px rgba(93, 64, 55, 0.2),
                -8px -8px 16px rgba(250, 243, 233, 0.7),
                inset 2px 2px 4px rgba(93, 64, 55, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .clay-card:hover {
            transform: translateY(-2px);
            box-shadow: 
                12px 12px 24px rgba(93, 64, 55, 0.25),
                -12px -12px 24px rgba(250, 243, 233, 0.8),
                inset 2px 2px 4px rgba(93, 64, 55, 0.1);
        }

        /* 凹陷效果 */
        .clay-inset {
            background: #E6D5C7;
            border-radius: 15px;
            box-shadow: 
                inset 4px 4px 8px rgba(93, 64, 55, 0.2),
                inset -4px -4px 8px rgba(250, 243, 233, 0.7);
        }

        /* 按钮样式 */
        .clay-btn {
            background: #E6D5C7;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            color: #5D4037;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                6px 6px 12px rgba(93, 64, 55, 0.2),
                -6px -6px 12px rgba(250, 243, 233, 0.7);
            text-align: center;
            margin: 10px 0;
            font-family: inherit;
        }

        .clay-btn:hover {
            transform: translateY(-1px);
        }

        .clay-btn:active {
            transform: translateY(1px);
            box-shadow: 
                inset 4px 4px 8px rgba(93, 64, 55, 0.2),
                inset -4px -4px 8px rgba(250, 243, 233, 0.7);
        }

        .clay-btn-primary {
            background: linear-gradient(135deg, #C39B7B, #B07D62);
            color: #FAF3E9;
            box-shadow: 
                6px 6px 12px rgba(93, 64, 55, 0.3),
                -6px -6px 12px rgba(250, 243, 233, 0.5);
        }

        /* 输入框样式 */
        .clay-input {
            width: 100%;
            background: #E6D5C7;
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: #5D4037;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 
                inset 4px 4px 8px rgba(93, 64, 55, 0.15),
                inset -4px -4px 8px rgba(250, 243, 233, 0.6);
            transition: all 0.3s ease;
        }

        .clay-input:focus {
            outline: none;
            box-shadow: 
                inset 6px 6px 12px rgba(93, 64, 55, 0.2),
                inset -6px -6px 12px rgba(250, 243, 233, 0.7);
        }

        .clay-input::placeholder {
            color: rgba(93, 64, 55, 0.6);
        }

        /* 页面内容区域 */
        .page-content {
            padding: 60px 20px 100px;
            height: 100%;
            overflow-y: auto;
        }

        .page-title {
            color: #4E3B31;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-align: center;
        }

        .subtitle {
            color: rgba(93, 64, 55, 0.8);
            font-size: 16px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 500;
        }

        /* 底部导航栏 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #E6D5C7;
            box-shadow: 
                0 -4px 8px rgba(93, 64, 55, 0.1),
                inset 0 1px 0 rgba(250, 243, 233, 0.5);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: rgba(93, 64, 55, 0.7);
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-item.active {
            color: #B07D62;
            transform: scale(1.1);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            fill: currentColor;
        }

        /* 统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #E6D5C7;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            box-shadow: 
                4px 4px 8px rgba(93, 64, 55, 0.15),
                -4px -4px 8px rgba(250, 243, 233, 0.6);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #B07D62;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(93, 64, 55, 0.8);
            font-weight: 500;
        }

        /* 进度条样式 */
        .progress-bar {
            height: 8px;
            background: #E6D5C7;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
            box-shadow: 
                inset 2px 2px 4px rgba(93, 64, 55, 0.2),
                inset -2px -2px 4px rgba(250, 243, 233, 0.7);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #C39B7B, #B07D62);
            border-radius: 4px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 2px 4px rgba(176, 125, 98, 0.3);
        }

        /* 响应式调整 */
        @media (max-width: 1600px) {
            .container {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 800px) {
            .container {
                grid-template-columns: 1fr;
            }
        }

        /* 3D插画容器 */
        .illustration-3d {
            width: 200px;
            height: 200px;
            margin: 50px auto 30px;
            position: relative;
            transform-style: preserve-3d;
            animation: float 4s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotateY(0deg); }
            25% { transform: translateY(-10px) rotateY(5deg); }
            50% { transform: translateY(-5px) rotateY(0deg); }
            75% { transform: translateY(-15px) rotateY(-5deg); }
        }

        /* 3D黏土球体 */
        .clay-sphere {
            width: 120px;
            height: 120px;
            background: radial-gradient(circle at 30% 30%, #FAF3E9, #C39B7B, #B07D62);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 
                15px 15px 30px rgba(93, 64, 55, 0.3),
                -5px -5px 15px rgba(250, 243, 233, 0.8),
                inset -10px -10px 20px rgba(93, 64, 55, 0.2),
                inset 10px 10px 20px rgba(250, 243, 233, 0.9);
        }

        .clay-sphere::before {
            content: '✨';
            position: absolute;
            top: 20%;
            left: 25%;
            font-size: 24px;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 折线图动画 */
        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        /* 输入组样式 */
        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            color: #4E3B31;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
    </style>
</head>
<body>
    <!-- SVG图标系统 -->
    <svg style="display: none;">
        <defs>
            <!-- 3D黏土风格图标定义 -->
            <symbol id="icon-home" viewBox="0 0 24 24">
                <path d="M12 3l8 8v10h-6v-6h-4v6H4V11l8-8z" fill="currentColor" stroke="none"/>
                <ellipse cx="12" cy="8" rx="1" ry="0.5" fill="rgba(250,243,233,0.8)"/>
            </symbol>
            
            <symbol id="icon-target" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="9" fill="currentColor"/>
                <circle cx="12" cy="12" r="6" fill="rgba(250,243,233,0.3)"/>
                <circle cx="12" cy="12" r="3" fill="currentColor"/>
                <ellipse cx="12" cy="8" rx="2" ry="1" fill="rgba(250,243,233,0.8)"/>
            </symbol>
            
            <symbol id="icon-heart" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="currentColor"/>
                <ellipse cx="12" cy="8" rx="3" ry="1.5" fill="rgba(250,243,233,0.8)"/>
            </symbol>
            
            <symbol id="icon-book" viewBox="0 0 24 24">
                <path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2z" fill="currentColor"/>
                <rect x="9" y="6" width="8" height="1" fill="rgba(250,243,233,0.8)"/>
            </symbol>
            
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3A8.994 8.994 0 0 0 13 3.06V1h-2v2.06A8.994 8.994 0 0 0 3.06 11H1v2h2.06A8.994 8.994 0 0 0 11 20.94V23h2v-2.06A8.994 8.994 0 0 0 20.94 13H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" fill="currentColor"/>
                <circle cx="12" cy="8" r="1" fill="rgba(250,243,233,0.8)"/>
            </symbol>
        </defs>
    </svg>

    <div class="container">
        <!-- 1. 启动页 (带3D插画) -->
        <div class="phone-frame">
            <div class="page-content" style="display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                <div class="illustration-3d">
                    <div class="clay-sphere"></div>
                </div>
                <h1 class="page-title" style="font-size: 36px; margin-bottom: 15px;">显化</h1>
                <p class="subtitle" style="font-size: 18px; margin-bottom: 40px;">让梦想在黏土中成形</p>
                <button class="clay-btn clay-btn-primary" style="padding: 16px 32px; font-size: 16px;">开始显化之旅</button>
            </div>
        </div>

        <!-- 2. 主页/仪表盘 -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">今日显化</h1>
                <p class="subtitle">你的能量正在塑造现实</p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">活跃目标</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">78%</div>
                        <div class="stat-label">完成度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">连续天数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">已实现</div>
                    </div>
                </div>

                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">今日肯定语</h3>
                    <p style="color: #5D4037; font-size: 16px; line-height: 1.6; text-align: center; font-style: italic; font-weight: 500;">
                        "我值得拥有我所渴望的一切，宇宙正在为我安排最好的结果。"
                    </p>
                </div>

                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">快速行动</h3>
                    <button class="clay-btn" style="width: 100%; margin: 5px 0;">📝 记录感恩</button>
                    <button class="clay-btn" style="width: 100%; margin: 5px 0;">🎯 查看目标</button>
                    <button class="clay-btn" style="width: 100%; margin: 5px 0;">🖼️ 愿景板</button>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 3. 目标列表页 -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">我的目标</h1>
                <p class="subtitle">专注于你想要的，而不是你害怕的</p>

                <button class="clay-btn clay-btn-primary" style="width: 100%; margin-bottom: 20px;">+ 添加新目标</button>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-size: 20px; margin-right: 10px;">💰</span>
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">财务自由</h4>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: rgba(93, 64, 55, 0.8);">
                        <span>65% 完成</span>
                        <span>还有 3 个月</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-size: 20px; margin-right: 10px;">❤️</span>
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">理想关系</h4>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 40%;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: rgba(93, 64, 55, 0.8);">
                        <span>40% 完成</span>
                        <span>还有 6 个月</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-size: 20px; margin-right: 10px;">🏠</span>
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">理想住所</h4>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: rgba(93, 64, 55, 0.8);">
                        <span>80% 完成</span>
                        <span>还有 1 个月</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-size: 20px; margin-right: 10px;">🌟</span>
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">事业成功</h4>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-size: 12px; color: rgba(93, 64, 55, 0.8);">
                        <span>90% 完成</span>
                        <span>即将实现</span>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 4. 目标详情页 (带动态折线图) -->
        <div class="phone-frame">
            <div class="page-content">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <button class="clay-btn" style="width: 40px; height: 40px; border-radius: 50%; padding: 0; margin-right: 15px;">←</button>
                    <h1 class="page-title" style="margin: 0; font-size: 24px;">💰 财务自由</h1>
                </div>

                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">目标详情</h3>
                    <p style="color: #5D4037; margin-bottom: 15px; line-height: 1.5;">
                        在未来12个月内实现月收入10万元，拥有足够的被动收入来支持理想的生活方式。
                    </p>
                    <div class="progress-bar" style="margin-bottom: 10px;">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                    <div style="color: rgba(93, 64, 55, 0.8); font-size: 14px;">
                        65% 完成 • 还有 3 个月
                    </div>
                </div>

                <!-- 动态折线图 -->
                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">进度趋势</h3>
                    <svg width="100%" height="120" viewBox="0 0 300 120" style="overflow: visible;">
                        <defs>
                            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" style="stop-color:#C39B7B"/>
                                <stop offset="100%" style="stop-color:#B07D62"/>
                            </linearGradient>
                        </defs>
                        <!-- 网格线 -->
                        <g stroke="rgba(93,64,55,0.1)" stroke-width="1">
                            <line x1="0" y1="20" x2="300" y2="20"/>
                            <line x1="0" y1="40" x2="300" y2="40"/>
                            <line x1="0" y1="60" x2="300" y2="60"/>
                            <line x1="0" y1="80" x2="300" y2="80"/>
                            <line x1="0" y1="100" x2="300" y2="100"/>
                        </g>
                        <!-- 动态折线 -->
                        <path d="M0,100 L50,85 L100,70 L150,60 L200,45 L250,35 L300,30"
                              fill="none"
                              stroke="url(#lineGradient)"
                              stroke-width="3"
                              stroke-linecap="round"
                              stroke-dasharray="400"
                              stroke-dashoffset="400"
                              style="animation: drawLine 3s ease-in-out forwards;">
                        </path>
                        <!-- 数据点 -->
                        <circle cx="0" cy="100" r="4" fill="#C39B7B" opacity="0" style="animation: fadeIn 3.5s ease-in-out forwards;"/>
                        <circle cx="50" cy="85" r="4" fill="#C39B7B" opacity="0" style="animation: fadeIn 4s ease-in-out forwards;"/>
                        <circle cx="100" cy="70" r="4" fill="#C39B7B" opacity="0" style="animation: fadeIn 4.5s ease-in-out forwards;"/>
                        <circle cx="150" cy="60" r="4" fill="#B07D62" opacity="0" style="animation: fadeIn 5s ease-in-out forwards;"/>
                        <circle cx="200" cy="45" r="4" fill="#B07D62" opacity="0" style="animation: fadeIn 5.5s ease-in-out forwards;"/>
                        <circle cx="250" cy="35" r="4" fill="#B07D62" opacity="0" style="animation: fadeIn 6s ease-in-out forwards;"/>
                        <circle cx="300" cy="30" r="4" fill="#B07D62" opacity="0" style="animation: fadeIn 6.5s ease-in-out forwards;"/>
                    </svg>
                    <div style="display: flex; justify-content: space-between; font-size: 10px; color: rgba(93, 64, 55, 0.6); margin-top: 10px;">
                        <span>1月</span>
                        <span>2月</span>
                        <span>3月</span>
                        <span>4月</span>
                        <span>5月</span>
                        <span>6月</span>
                        <span>7月</span>
                    </div>
                </div>

                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">关键里程碑</h3>
                    <div style="margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: #FAF3E9; font-size: 12px; box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.2);">✓</div>
                            <span style="color: #5D4037;">建立多元收入来源</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: #FAF3E9; font-size: 12px; box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.2);">✓</div>
                            <span style="color: #5D4037;">投资理财知识学习</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: #E6D5C7; border: 2px solid #C39B7B; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: #C39B7B; font-size: 12px; box-shadow: inset 2px 2px 4px rgba(93, 64, 55, 0.1);">○</div>
                            <span style="color: #5D4037;">达到月收入8万元</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background: #E6D5C7; border: 2px solid rgba(195, 155, 123, 0.5); border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: rgba(195, 155, 123, 0.5); font-size: 12px; box-shadow: inset 2px 2px 4px rgba(93, 64, 55, 0.1);">○</div>
                            <span style="color: rgba(93, 64, 55, 0.7);">实现月收入10万元</span>
                        </div>
                    </div>
                </div>

                <button class="clay-btn clay-btn-primary" style="width: 100%;">编辑目标</button>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>

        <!-- 5. 创建/编辑目标页 -->
        <div class="phone-frame">
            <div class="page-content">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <button class="clay-btn" style="width: 40px; height: 40px; border-radius: 50%; padding: 0; margin-right: 15px;">←</button>
                    <h1 class="page-title" style="margin: 0; font-size: 24px;">创建新目标</h1>
                </div>

                <div class="clay-card">
                    <div class="input-group">
                        <label class="input-label">目标标题</label>
                        <input type="text" class="clay-input" placeholder="例如：实现财务自由">
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标描述</label>
                        <textarea class="clay-input" rows="3" placeholder="详细描述你的目标..." style="resize: vertical; min-height: 80px;"></textarea>
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标分类</label>
                        <select class="clay-input">
                            <option>💰 财务</option>
                            <option>❤️ 关系</option>
                            <option>🏠 生活</option>
                            <option>🌟 事业</option>
                            <option>💪 健康</option>
                            <option>🎓 学习</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标期限</label>
                        <input type="date" class="clay-input">
                    </div>

                    <div class="input-group">
                        <label class="input-label">重要程度</label>
                        <div style="display: flex; gap: 10px; margin-top: 8px;">
                            <button class="clay-btn" style="flex: 1; font-size: 12px;">低</button>
                            <button class="clay-btn clay-btn-primary" style="flex: 1; font-size: 12px;">中</button>
                            <button class="clay-btn" style="flex: 1; font-size: 12px;">高</button>
                        </div>
                    </div>
                </div>

                <button class="clay-btn clay-btn-primary" style="width: 100%; margin-bottom: 10px;">保存目标</button>
                <button class="clay-btn" style="width: 100%;">取消</button>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 6. 肯定语列表页 -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">肯定语</h1>
                <p class="subtitle">用积极的话语重塑你的现实</p>

                <div class="clay-card" style="background: linear-gradient(135deg, #C39B7B, #B07D62); color: #FAF3E9; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px; text-align: center; font-weight: 600;">💫 今日推荐</h3>
                    <p style="font-size: 18px; line-height: 1.6; text-align: center; font-style: italic; font-weight: 500;">
                        "我是自己生命的创造者，每一天都充满无限可能。"
                    </p>
                    <div style="text-align: center; margin-top: 15px;">
                        <button class="clay-btn" style="display: inline-block; margin: 0 5px; background: rgba(250, 243, 233, 0.2);">❤️ 收藏</button>
                        <button class="clay-btn" style="display: inline-block; margin: 0 5px; background: rgba(250, 243, 233, 0.2);">🔄 换一句</button>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; margin-bottom: 20px; overflow-x: auto; padding-bottom: 5px;">
                    <button class="clay-btn clay-btn-primary" style="white-space: nowrap; font-size: 12px;">💰 财富</button>
                    <button class="clay-btn" style="white-space: nowrap; font-size: 12px;">❤️ 爱情</button>
                    <button class="clay-btn" style="white-space: nowrap; font-size: 12px;">🌟 成功</button>
                    <button class="clay-btn" style="white-space: nowrap; font-size: 12px;">💪 健康</button>
                    <button class="clay-btn" style="white-space: nowrap; font-size: 12px;">🧘 内心</button>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <p style="color: #5D4037; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "金钱是能量的流动，我允许它自由地流向我。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(93, 64, 55, 0.7); font-size: 12px;">财富类</span>
                        <div style="color: #C39B7B;">❤️ 💬 📤</div>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <p style="color: #5D4037; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "我值得被爱，也有能力给予真挚的爱。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(93, 64, 55, 0.7); font-size: 12px;">爱情类</span>
                        <div style="color: #C39B7B;">❤️ 💬 📤</div>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <p style="color: #5D4037; font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "每一个挑战都是我成长和学习的机会。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(93, 64, 55, 0.7); font-size: 12px;">成功类</span>
                        <div style="color: #C39B7B;">❤️ 💬 📤</div>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 7. 感恩日记页 -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">感恩日记</h1>
                <p class="subtitle">感恩是显化的最高频率</p>

                <button class="clay-btn clay-btn-primary" style="width: 100%; margin-bottom: 20px;">+ 记录今日感恩</button>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">今天 • 6月18日</h4>
                        <div style="color: #C39B7B; font-size: 18px;">😊</div>
                    </div>
                    <p style="color: #5D4037; line-height: 1.5; margin-bottom: 10px;">
                        感恩今天遇到的每一个美好瞬间，感恩家人的陪伴，感恩工作中的成长机会。每一天都是礼物，我用心感受生活的美好。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#家人</span>
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#成长</span>
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#美好</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">昨天 • 6月17日</h4>
                        <div style="color: #C39B7B; font-size: 18px;">🙏</div>
                    </div>
                    <p style="color: #5D4037; line-height: 1.5; margin-bottom: 10px;">
                        感恩朋友的支持和鼓励，感恩身体的健康，感恩有机会追求自己的梦想。宇宙总是在最合适的时候给我最需要的。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#友谊</span>
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#健康</span>
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#梦想</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">6月16日</h4>
                        <div style="color: #C39B7B; font-size: 18px;">✨</div>
                    </div>
                    <p style="color: #5D4037; line-height: 1.5; margin-bottom: 10px;">
                        感恩今天收到的意外惊喜，感恩生活中的小确幸，感恩每一个让我微笑的瞬间。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#惊喜</span>
                        <span style="background: rgba(195, 155, 123, 0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: #B07D62;">#小确幸</span>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 8. 愿景板 (带Unsplash图片) -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">愿景板</h1>
                <p class="subtitle">可视化你的梦想，让它们成为现实</p>

                <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                    <button class="clay-btn clay-btn-primary" style="flex: 1;">+ 添加图片</button>
                    <button class="clay-btn" style="flex: 1;">📝 添加文字</button>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px;">
                    <div class="clay-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                        <img src="https://source.unsplash.com/200x200/?luxury,house,home" alt="理想住所" style="width: 100%; height: 100%; object-fit: cover;">
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(93, 64, 55, 0.8)); padding: 10px; color: #FAF3E9; font-size: 12px; font-weight: 600;">
                            🏠 理想住所
                        </div>
                    </div>
                    <div class="clay-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                        <img src="https://source.unsplash.com/200x200/?luxury,car,vehicle" alt="梦想座驾" style="width: 100%; height: 100%; object-fit: cover;">
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(93, 64, 55, 0.8)); padding: 10px; color: #FAF3E9; font-size: 12px; font-weight: 600;">
                            🚗 梦想座驾
                        </div>
                    </div>
                    <div class="clay-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                        <img src="https://source.unsplash.com/200x200/?travel,beach,vacation" alt="环球旅行" style="width: 100%; height: 100%; object-fit: cover;">
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(93, 64, 55, 0.8)); padding: 10px; color: #FAF3E9; font-size: 12px; font-weight: 600;">
                            ✈️ 环球旅行
                        </div>
                    </div>
                    <div class="clay-card" style="padding: 0; overflow: hidden; aspect-ratio: 1;">
                        <img src="https://source.unsplash.com/200x200/?money,success,business" alt="财务自由" style="width: 100%; height: 100%; object-fit: cover;">
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(93, 64, 55, 0.8)); padding: 10px; color: #FAF3E9; font-size: 12px; font-weight: 600;">
                            💰 财务自由
                        </div>
                    </div>
                </div>

                <div class="clay-card">
                    <h3 style="color: #4E3B31; margin-bottom: 15px; font-weight: 600;">愿景宣言</h3>
                    <p style="color: #5D4037; font-size: 16px; line-height: 1.6; text-align: center; font-style: italic;">
                        "我正在创造一个充满爱、丰盛和自由的生活。我的梦想正在一步步实现，我感恩这个美好的显化过程。"
                    </p>
                </div>

                <div style="display: flex; gap: 10px;">
                    <button class="clay-btn" style="flex: 1;">📤 分享</button>
                    <button class="clay-btn" style="flex: 1;">💾 保存</button>
                    <button class="clay-btn" style="flex: 1;">✏️ 编辑</button>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 9. 设置页 -->
        <div class="phone-frame">
            <div class="page-content">
                <h1 class="page-title">设置</h1>
                <p class="subtitle">个性化你的显化之旅</p>

                <div class="clay-card" style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 15px; color: #FAF3E9; box-shadow: 4px 4px 8px rgba(93, 64, 55, 0.2);">👤</div>
                        <div>
                            <h3 style="color: #4E3B31; margin: 0 0 5px 0; font-weight: 600;">显化者</h3>
                            <p style="color: rgba(93, 64, 55, 0.8); margin: 0; font-size: 14px;">开始显化之旅 12 天</p>
                        </div>
                    </div>
                    <button class="clay-btn" style="width: 100%;">编辑个人资料</button>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="color: #4E3B31; margin: 0 0 5px 0; font-weight: 600;">🔔 每日提醒</h4>
                            <p style="color: rgba(93, 64, 55, 0.7); margin: 0; font-size: 12px;">每天 9:00 提醒你记录感恩</p>
                        </div>
                        <div style="width: 50px; height: 25px; background: linear-gradient(135deg, #C39B7B, #B07D62); border-radius: 25px; position: relative; box-shadow: inset 2px 2px 4px rgba(93, 64, 55, 0.2);">
                            <div style="width: 21px; height: 21px; background: #FAF3E9; border-radius: 50%; position: absolute; top: 2px; right: 2px; box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.2);"></div>
                        </div>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="color: #4E3B31; margin: 0 0 5px 0; font-weight: 600;">🌙 夜间模式</h4>
                            <p style="color: rgba(93, 64, 55, 0.7); margin: 0; font-size: 12px;">保护你的眼睛</p>
                        </div>
                        <div style="width: 50px; height: 25px; background: #E6D5C7; border-radius: 25px; position: relative; box-shadow: inset 2px 2px 4px rgba(93, 64, 55, 0.2);">
                            <div style="width: 21px; height: 21px; background: #FAF3E9; border-radius: 50%; position: absolute; top: 2px; left: 2px; box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.2);"></div>
                        </div>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <h4 style="color: #4E3B31; margin: 0 0 10px 0; font-weight: 600;">🎨 主题选择</h4>
                    <div style="display: flex; gap: 10px;">
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #E6D5C7, #C39B7B); border-radius: 50%; border: 3px solid #B07D62; box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.2);"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #D3C4B3, #B8A082); border-radius: 50%; border: 1px solid rgba(93, 64, 55, 0.3); box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.1);"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #F0E5D8, #E0D0B8); border-radius: 50%; border: 1px solid rgba(93, 64, 55, 0.3); box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.1);"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #DCC5A0, #C8A882); border-radius: 50%; border: 1px solid rgba(93, 64, 55, 0.3); box-shadow: 2px 2px 4px rgba(93, 64, 55, 0.1);"></div>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">📊 数据统计</h4>
                        <span style="color: #C39B7B;">→</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">💾 数据备份</h4>
                        <span style="color: #C39B7B;">→</span>
                    </div>
                </div>

                <div class="clay-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">❓ 帮助与支持</h4>
                        <span style="color: #C39B7B;">→</span>
                    </div>
                </div>

                <div class="clay-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="color: #4E3B31; margin: 0; font-weight: 600;">ℹ️ 关于显化</h4>
                        <span style="color: #C39B7B;">→</span>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-home"></use></svg>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-target"></use></svg>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-heart"></use></svg>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon"><use href="#icon-book"></use></svg>
                    <span>日记</span>
                </div>
                <div class="nav-item active">
                    <svg class="nav-icon"><use href="#icon-settings"></use></svg>
                    <span>设置</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        </div>
