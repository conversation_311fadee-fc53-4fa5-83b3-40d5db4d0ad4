<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显化应用 UI 原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #ccc;
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            background-size: cover;
            background-position: center;
            backdrop-filter: blur(10px);
        }

        /* 液体玻璃效果基础样式 */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* 底部导航栏 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(30px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            font-size: 12px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            opacity: 1;
            transform: scale(1.1);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background: currentColor;
            mask-size: contain;
            mask-repeat: no-repeat;
            mask-position: center;
        }

        /* 页面内容区域 */
        .page-content {
            padding: 60px 20px 100px;
            height: 100%;
            overflow-y: auto;
        }

        .page-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: center;
            margin-bottom: 30px;
        }

        /* 启动页样式 */
        .splash-screen {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 375 812"><defs><linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23ff9a9e"/><stop offset="100%" style="stop-color:%23fecfef"/></linearGradient></defs><rect width="375" height="812" fill="url(%23bg1)"/></svg>');
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .app-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 主页样式 */
        .dashboard {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 375 812"><defs><linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea"/><stop offset="100%" style="stop-color:%23764ba2"/></linearGradient></defs><rect width="375" height="812" fill="url(%23bg2)"/></svg>');
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        /* 目标列表样式 */
        .goals-list {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 375 812"><defs><linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23ffecd2"/><stop offset="100%" style="stop-color:%23fcb69f"/></linearGradient></defs><rect width="375" height="812" fill="url(%23bg3)"/></svg>');
        }

        .goal-item {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .goal-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .progress-bar {
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #feca57);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .goal-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        /* 按钮样式 */
        .btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            margin: 10px 0;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
        }

        /* 输入框样式 */
        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            color: white;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
        }

        .input-field {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            font-size: 16px;
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* 增强液体玻璃效果 */
        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: inherit;
            pointer-events: none;
        }

        .glass-card {
            position: relative;
            overflow: hidden;
        }

        /* 悬浮动画 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .app-logo {
            animation: float 3s ease-in-out infinite;
        }

        /* 脉冲效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .nav-item.active .nav-icon {
            animation: pulse 2s ease-in-out infinite;
        }

        /* 渐变文字效果 */
        .gradient-text {
            background: linear-gradient(135deg, #667eea, #764ba2, #ff9a9e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 毛玻璃按钮增强 */
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .btn {
            position: relative;
            overflow: hidden;
        }

        .btn:hover::before {
            opacity: 1;
        }

        /* 进度条动画 */
        .progress-fill {
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 输入框聚焦效果 */
        .input-field:focus {
            outline: none;
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 0 20px rgba(255,255,255,0.2);
            transform: scale(1.02);
        }

        /* 卡片阴影层次 */
        .glass-card {
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
        }

        /* 响应式调整 */
        @media (max-width: 1600px) {
            .container {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 800px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 1. 启动页 -->
        <div class="phone-frame splash-screen">
            <div class="app-logo">✨</div>
            <h1 class="page-title">显化</h1>
            <p class="subtitle">让梦想成为现实</p>
            <div class="btn btn-primary">开始旅程</div>
        </div>

        <!-- 2. 主页/仪表盘 -->
        <div class="phone-frame dashboard">
            <div class="page-content">
                <h1 class="page-title">今日显化</h1>
                <p class="subtitle">你的能量创造你的现实</p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">活跃目标</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">78%</div>
                        <div class="stat-label">完成度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">连续天数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">已实现</div>
                    </div>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">今日肯定语</h3>
                    <p style="color: rgba(255,255,255,0.9); font-size: 16px; line-height: 1.5; text-align: center; font-style: italic;">
                        "我值得拥有我所渴望的一切，宇宙正在为我安排最好的结果。"
                    </p>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">快速行动</h3>
                    <div class="btn" style="margin: 5px 0;">📝 记录感恩</div>
                    <div class="btn" style="margin: 5px 0;">🎯 查看目标</div>
                    <div class="btn" style="margin: 5px 0;">🖼️ 愿景板</div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item active">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 3. 目标列表页 -->
        <div class="phone-frame goals-list">
            <div class="page-content">
                <h1 class="page-title">我的目标</h1>
                <p class="subtitle">专注于你想要的，而不是你害怕的</p>

                <div class="btn btn-primary" style="margin-bottom: 20px;">+ 添加新目标</div>

                <div class="goal-item">
                    <div class="goal-title">💰 财务自由</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                    <div class="goal-meta">
                        <span>65% 完成</span>
                        <span>还有 3 个月</span>
                    </div>
                </div>

                <div class="goal-item">
                    <div class="goal-title">❤️ 理想关系</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 40%;"></div>
                    </div>
                    <div class="goal-meta">
                        <span>40% 完成</span>
                        <span>还有 6 个月</span>
                    </div>
                </div>

                <div class="goal-item">
                    <div class="goal-title">🏠 理想住所</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%;"></div>
                    </div>
                    <div class="goal-meta">
                        <span>80% 完成</span>
                        <span>还有 1 个月</span>
                    </div>
                </div>

                <div class="goal-item">
                    <div class="goal-title">🚗 梦想座驾</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 25%;"></div>
                    </div>
                    <div class="goal-meta">
                        <span>25% 完成</span>
                        <span>还有 12 个月</span>
                    </div>
                </div>

                <div class="goal-item">
                    <div class="goal-title">🌟 事业成功</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%;"></div>
                    </div>
                    <div class="goal-meta">
                        <span>90% 完成</span>
                        <span>即将实现</span>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 4. 目标详情页 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg4&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23a8edea&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23fed6e3&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg4)&quot;/></svg>');">
            <div class="page-content">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <div style="color: white; font-size: 18px; margin-right: 10px;">←</div>
                    <h1 class="page-title" style="margin: 0; font-size: 24px;">💰 财务自由</h1>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">目标详情</h3>
                    <p style="color: rgba(255,255,255,0.9); margin-bottom: 15px;">
                        在未来12个月内实现月收入10万元，拥有足够的被动收入来支持理想的生活方式。
                    </p>
                    <div class="progress-bar" style="margin-bottom: 10px;">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                    <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                        65% 完成 • 还有 3 个月
                    </div>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">关键里程碑</h3>
                    <div style="margin-bottom: 10px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: #4CAF50; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">✓</div>
                            <span style="color: rgba(255,255,255,0.9);">建立多元收入来源</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: #4CAF50; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">✓</div>
                            <span style="color: rgba(255,255,255,0.9);">投资理财知识学习</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div style="width: 20px; height: 20px; background: #FF9800; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">○</div>
                            <span style="color: rgba(255,255,255,0.9);">达到月收入8万元</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background: rgba(255,255,255,0.3); border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">○</div>
                            <span style="color: rgba(255,255,255,0.7);">实现月收入10万元</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">相关肯定语</h3>
                    <p style="color: rgba(255,255,255,0.9); font-style: italic; text-align: center; margin-bottom: 10px;">
                        "金钱轻松地流向我，我值得拥有财富和丰盛。"
                    </p>
                    <p style="color: rgba(255,255,255,0.9); font-style: italic; text-align: center;">
                        "我的收入每天都在增长，机会无处不在。"
                    </p>
                </div>

                <div class="btn btn-primary">编辑目标</div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 5. 创建/编辑目标页 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg5&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23ff9a9e&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23fad0c4&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg5)&quot;/></svg>');">
            <div class="page-content">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <div style="color: white; font-size: 18px; margin-right: 10px;">←</div>
                    <h1 class="page-title" style="margin: 0; font-size: 24px;">创建新目标</h1>
                </div>

                <div class="glass-card">
                    <div class="input-group">
                        <label class="input-label">目标标题</label>
                        <input type="text" class="input-field" placeholder="例如：实现财务自由">
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标描述</label>
                        <textarea class="input-field" rows="3" placeholder="详细描述你的目标..."></textarea>
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标分类</label>
                        <select class="input-field">
                            <option>💰 财务</option>
                            <option>❤️ 关系</option>
                            <option>🏠 生活</option>
                            <option>🌟 事业</option>
                            <option>💪 健康</option>
                            <option>🎓 学习</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label class="input-label">目标期限</label>
                        <input type="date" class="input-field">
                    </div>

                    <div class="input-group">
                        <label class="input-label">重要程度</label>
                        <div style="display: flex; gap: 10px; margin-top: 8px;">
                            <div style="flex: 1; text-align: center; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 8px; color: white;">低</div>
                            <div style="flex: 1; text-align: center; padding: 10px; background: rgba(255,255,255,0.4); border-radius: 8px; color: white;">中</div>
                            <div style="flex: 1; text-align: center; padding: 10px; background: rgba(255,255,255,0.6); border-radius: 8px; color: white;">高</div>
                        </div>
                    </div>
                </div>

                <div class="btn btn-primary">保存目标</div>
                <div class="btn">取消</div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 6. 肯定语列表页 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg6&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23ffecd2&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23fcb69f&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg6)&quot;/></svg>');">
            <div class="page-content">
                <h1 class="page-title">肯定语</h1>
                <p class="subtitle">用积极的话语重塑你的现实</p>

                <div class="glass-card" style="background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)); margin-bottom: 20px;">
                    <h3 style="color: white; margin-bottom: 15px; text-align: center;">💫 今日推荐</h3>
                    <p style="color: white; font-size: 18px; line-height: 1.6; text-align: center; font-style: italic; font-weight: 500;">
                        "我是自己生命的创造者，每一天都充满无限可能。"
                    </p>
                    <div style="text-align: center; margin-top: 15px;">
                        <div class="btn" style="display: inline-block; margin: 0 5px;">❤️ 收藏</div>
                        <div class="btn" style="display: inline-block; margin: 0 5px;">🔄 换一句</div>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; margin-bottom: 20px; overflow-x: auto;">
                    <div class="btn" style="white-space: nowrap; background: rgba(255,255,255,0.3);">💰 财富</div>
                    <div class="btn" style="white-space: nowrap;">❤️ 爱情</div>
                    <div class="btn" style="white-space: nowrap;">🌟 成功</div>
                    <div class="btn" style="white-space: nowrap;">💪 健康</div>
                    <div class="btn" style="white-space: nowrap;">🧘 内心</div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <p style="color: rgba(255,255,255,0.95); font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "金钱是能量的流动，我允许它自由地流向我。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(255,255,255,0.7); font-size: 12px;">财富类</span>
                        <div style="color: rgba(255,255,255,0.8);">❤️ 💬 📤</div>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <p style="color: rgba(255,255,255,0.95); font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "我值得被爱，也有能力给予真挚的爱。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(255,255,255,0.7); font-size: 12px;">爱情类</span>
                        <div style="color: rgba(255,255,255,0.8);">❤️ 💬 📤</div>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <p style="color: rgba(255,255,255,0.95); font-size: 16px; line-height: 1.5; margin-bottom: 10px;">
                        "每一个挑战都是我成长和学习的机会。"
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: rgba(255,255,255,0.7); font-size: 12px;">成功类</span>
                        <div style="color: rgba(255,255,255,0.8);">❤️ 💬 📤</div>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 7. 感恩日记页 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg7&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23a8edea&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23fed6e3&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg7)&quot;/></svg>');">
            <div class="page-content">
                <h1 class="page-title">感恩日记</h1>
                <p class="subtitle">感恩是显化的最高频率</p>

                <div class="btn btn-primary" style="margin-bottom: 20px;">+ 记录今日感恩</div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: white; margin: 0;">今天 • 6月18日</h4>
                        <div style="color: rgba(255,255,255,0.8);">😊 开心</div>
                    </div>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.5; margin-bottom: 10px;">
                        感恩今天遇到的每一个美好瞬间，感恩家人的陪伴，感恩工作中的成长机会。每一天都是礼物，我用心感受生活的美好。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#家人</span>
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#成长</span>
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#美好</span>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: white; margin: 0;">昨天 • 6月17日</h4>
                        <div style="color: rgba(255,255,255,0.8);">🙏 感恩</div>
                    </div>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.5; margin-bottom: 10px;">
                        感恩朋友的支持和鼓励，感恩身体的健康，感恩有机会追求自己的梦想。宇宙总是在最合适的时候给我最需要的。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#友谊</span>
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#健康</span>
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#梦想</span>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="color: white; margin: 0;">6月16日</h4>
                        <div style="color: rgba(255,255,255,0.8);">✨ 惊喜</div>
                    </div>
                    <p style="color: rgba(255,255,255,0.9); line-height: 1.5; margin-bottom: 10px;">
                        感恩今天收到的意外惊喜，感恩生活中的小确幸，感恩每一个让我微笑的瞬间。
                    </p>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#惊喜</span>
                        <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px; font-size: 12px; color: white;">#小确幸</span>
                    </div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 8. 愿景板 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg8&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23667eea&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23764ba2&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg8)&quot;/></svg>');">
            <div class="page-content">
                <h1 class="page-title">愿景板</h1>
                <p class="subtitle">可视化你的梦想，让它们成为现实</p>

                <div style="display: flex; gap: 10px; margin-bottom: 20px;">
                    <div class="btn btn-primary" style="flex: 1;">+ 添加图片</div>
                    <div class="btn" style="flex: 1;">📝 添加文字</div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px;">
                    <div style="aspect-ratio: 1; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🏠</div>
                            <div style="font-size: 12px;">理想住所</div>
                        </div>
                    </div>
                    <div style="aspect-ratio: 1; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🚗</div>
                            <div style="font-size: 12px;">梦想座驾</div>
                        </div>
                    </div>
                    <div style="aspect-ratio: 1; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 24px; margin-bottom: 8px;">✈️</div>
                            <div style="font-size: 12px;">环球旅行</div>
                        </div>
                    </div>
                    <div style="aspect-ratio: 1; background: rgba(255,255,255,0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.3);">
                        <div style="text-align: center; color: white;">
                            <div style="font-size: 24px; margin-bottom: 8px;">💰</div>
                            <div style="font-size: 12px;">财务自由</div>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <h3 style="color: white; margin-bottom: 15px;">愿景宣言</h3>
                    <p style="color: rgba(255,255,255,0.9); font-size: 16px; line-height: 1.6; text-align: center; font-style: italic;">
                        "我正在创造一个充满爱、丰盛和自由的生活。我的梦想正在一步步实现，我感恩这个美好的显化过程。"
                    </p>
                </div>

                <div style="display: flex; gap: 10px;">
                    <div class="btn" style="flex: 1;">📤 分享</div>
                    <div class="btn" style="flex: 1;">💾 保存</div>
                    <div class="btn" style="flex: 1;">✏️ 编辑</div>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>

        <!-- 9. 设置页 -->
        <div class="phone-frame" style="background-image: url('data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 375 812&quot;><defs><linearGradient id=&quot;bg9&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:%23ff9a9e&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:%23fecfef&quot;/></linearGradient></defs><rect width=&quot;375&quot; height=&quot;812&quot; fill=&quot;url(%23bg9)&quot;/></svg>');">
            <div class="page-content">
                <h1 class="page-title">设置</h1>
                <p class="subtitle">个性化你的显化之旅</p>

                <div class="glass-card" style="margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.3); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 15px;">👤</div>
                        <div>
                            <h3 style="color: white; margin: 0 0 5px 0;">显化者</h3>
                            <p style="color: rgba(255,255,255,0.8); margin: 0; font-size: 14px;">开始显化之旅 12 天</p>
                        </div>
                    </div>
                    <div class="btn" style="margin: 0;">编辑个人资料</div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="color: white; margin: 0 0 5px 0;">🔔 每日提醒</h4>
                            <p style="color: rgba(255,255,255,0.7); margin: 0; font-size: 12px;">每天 9:00 提醒你记录感恩</p>
                        </div>
                        <div style="width: 50px; height: 25px; background: #4CAF50; border-radius: 25px; position: relative;">
                            <div style="width: 21px; height: 21px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px;"></div>
                        </div>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="color: white; margin: 0 0 5px 0;">🌙 夜间模式</h4>
                            <p style="color: rgba(255,255,255,0.7); margin: 0; font-size: 12px;">保护你的眼睛</p>
                        </div>
                        <div style="width: 50px; height: 25px; background: rgba(255,255,255,0.3); border-radius: 25px; position: relative;">
                            <div style="width: 21px; height: 21px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px;"></div>
                        </div>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <h4 style="color: white; margin: 0 0 10px 0;">🎨 主题选择</h4>
                    <div style="display: flex; gap: 10px;">
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; border: 2px solid white;"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #ff9a9e, #fecfef); border-radius: 50%; border: 1px solid rgba(255,255,255,0.3);"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #a8edea, #fed6e3); border-radius: 50%; border: 1px solid rgba(255,255,255,0.3);"></div>
                        <div style="width: 30px; height: 30px; background: linear-gradient(135deg, #ffecd2, #fcb69f); border-radius: 50%; border: 1px solid rgba(255,255,255,0.3);"></div>
                    </div>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <h4 style="color: white; margin: 0 0 10px 0; display: flex; align-items: center;">
                        📊 数据统计
                        <span style="margin-left: 10px;">→</span>
                    </h4>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <h4 style="color: white; margin: 0 0 10px 0; display: flex; align-items: center;">
                        💾 数据备份
                        <span style="margin-left: 10px;">→</span>
                    </h4>
                </div>

                <div class="glass-card" style="margin-bottom: 15px;">
                    <h4 style="color: white; margin: 0 0 10px 0; display: flex; align-items: center;">
                        ❓ 帮助与支持
                        <span style="margin-left: 10px;">→</span>
                    </h4>
                </div>

                <div class="glass-card">
                    <h4 style="color: white; margin: 0 0 10px 0; display: flex; align-items: center;">
                        ℹ️ 关于显化
                        <span style="margin-left: 10px;">→</span>
                    </h4>
                </div>
            </div>

            <div class="bottom-nav">
                <div class="nav-item">
                    <div class="nav-icon">🏠</div>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🎯</div>
                    <span>目标</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">💭</div>
                    <span>肯定语</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📖</div>
                    <span>日记</span>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">⚙️</div>
                    <span>设置</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
